from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count
from django.utils import timezone
from datetime import date, timedelta, datetime
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
import json

from .models import (
    Employee, Department, Designation, LeaveType,
    HolidayCalendar, LeaveBalance, LeaveRequest, Attendance
)

# Dashboard Views
@login_required
def leave_attendance_dashboard(request):
    """Main dashboard for Leave & Attendance Management"""
    try:
        employee = request.user.employee_profile
    except:
        # If user doesn't have employee profile, redirect to create one
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    # Get current year data
    current_year = date.today().year
    current_month = date.today().month

    # Leave Statistics
    leave_balances = LeaveBalance.objects.filter(employee=employee, year=current_year)
    total_leave_balance = sum([lb.remaining_days for lb in leave_balances])

    # Recent leave requests
    recent_leaves = LeaveRequest.objects.filter(employee=employee).order_by('-applied_on')[:5]

    # Attendance Statistics for current month
    attendance_records = Attendance.objects.filter(
        employee=employee,
        date__year=current_year,
        date__month=current_month
    )

    present_days = attendance_records.filter(status='Present').count()
    absent_days = attendance_records.filter(status='Absent').count()
    leave_days = attendance_records.filter(status='Leave').count()
    wfh_days = attendance_records.filter(status='WFH').count()

    # Working hours this month
    total_working_hours = attendance_records.aggregate(
        total=Sum('working_hours')
    )['total'] or 0

    # Upcoming holidays
    upcoming_holidays = HolidayCalendar.objects.filter(
        date__gte=date.today()
    ).order_by('date')[:5]

    context = {
        'employee': employee,
        'total_leave_balance': total_leave_balance,
        'leave_balances': leave_balances,
        'recent_leaves': recent_leaves,
        'present_days': present_days,
        'absent_days': absent_days,
        'leave_days': leave_days,
        'wfh_days': wfh_days,
        'total_working_hours': total_working_hours,
        'upcoming_holidays': upcoming_holidays,
        'current_month': date.today().strftime('%B %Y'),
    }

    return render(request, 'core/dashboard.html', context)

@login_required
def admin_leave_dashboard(request):
    """Admin dashboard for Leave & Attendance Management"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('leave_attendance_dashboard')

    # Get statistics
    total_employees = Employee.objects.filter(status='Active').count()
    pending_leaves = LeaveRequest.objects.filter(status='Pending').count()

    # Today's attendance
    today = date.today()
    today_attendance = Attendance.objects.filter(date=today)
    present_today = today_attendance.filter(status='Present').count()
    absent_today = today_attendance.filter(status='Absent').count()

    # Recent leave requests
    recent_leave_requests = LeaveRequest.objects.select_related(
        'employee__user', 'leave_type'
    ).order_by('-applied_on')[:10]

    # Department wise statistics
    departments = Department.objects.annotate(
        employee_count=Count('employee', filter=Q(employee__status='Active'))
    )

    context = {
        'total_employees': total_employees,
        'pending_leaves': pending_leaves,
        'present_today': present_today,
        'absent_today': absent_today,
        'recent_leave_requests': recent_leave_requests,
        'departments': departments,
    }

    return render(request, 'core/admin_dashboard.html', context)

# Employee Profile Management
@login_required
def create_employee_profile(request):
    """Create employee profile for users who don't have one"""
    try:
        # Check if user already has an employee profile
        employee = request.user.employee_profile
        messages.info(request, 'You already have an employee profile.')
        return redirect('core:dashboard')
    except:
        pass

    if request.method == 'POST':
        from .forms import EmployeeForm
        form = EmployeeForm(request.POST)
        if form.is_valid():
            employee = form.save(commit=False)
            employee.user = request.user
            employee.save()
            messages.success(request, 'Employee profile created successfully!')
            return redirect('core:dashboard')
    else:
        from .forms import EmployeeForm
        form = EmployeeForm()

    return render(request, 'core/employee_profile_form.html', {'form': form})

# API Views for AJAX calls
@login_required
def check_leave_balance(request):
    """API endpoint to check leave balances"""
    try:
        employee = request.user.employee_profile
        current_year = date.today().year
        balances = LeaveBalance.objects.filter(employee=employee, year=current_year)

        balance_data = []
        for balance in balances:
            balance_data.append({
                'leave_type': balance.leave_type.name,
                'total_allocated': balance.total_allocated,
                'used_days': float(balance.used_days),
                'remaining_days': balance.remaining_days
            })

        return JsonResponse({'balances': balance_data})
    except:
        return JsonResponse({'balances': []})

@login_required
@require_POST
def calculate_leave_days(request):
    """API endpoint to calculate leave days"""
    try:
        data = json.loads(request.body)
        start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
        is_half_day = data.get('is_half_day', False)

        if is_half_day:
            total_days = 0.5
        else:
            # Simple calculation - can be enhanced to exclude weekends/holidays
            total_days = (end_date - start_date).days + 1

            # Exclude weekends
            current_date = start_date
            weekend_days = 0
            while current_date <= end_date:
                if current_date.weekday() >= 5:  # Saturday=5, Sunday=6
                    weekend_days += 1
                current_date += timedelta(days=1)

            total_days -= weekend_days

            # Exclude holidays
            holidays = HolidayCalendar.objects.filter(
                date__range=[start_date, end_date]
            ).count()
            total_days -= holidays

            total_days = max(0, total_days)

        return JsonResponse({'total_days': total_days})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)

# Leave Request Views
@login_required
def leave_request_create(request):
    """Create a new leave request"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    if request.method == 'POST':
        from .forms import LeaveRequestForm
        form = LeaveRequestForm(request.POST, employee=employee)
        if form.is_valid():
            leave_request = form.save(commit=False)
            leave_request.employee = employee
            leave_request.save()
            messages.success(request, 'Leave request submitted successfully!')
            return redirect('core:leave_request_list')
    else:
        from .forms import LeaveRequestForm
        form = LeaveRequestForm(employee=employee)

    return render(request, 'core/leave_request_form.html', {'form': form})

@login_required
def leave_request_list(request):
    """List all leave requests for the current user or all if admin"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    if request.user.role == 'admin':
        leave_requests = LeaveRequest.objects.select_related('employee__user', 'leave_type').order_by('-applied_on')
    else:
        leave_requests = LeaveRequest.objects.filter(employee=employee).order_by('-applied_on')

    # Pagination
    paginator = Paginator(leave_requests, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'is_admin': request.user.role == 'admin'
    }

    return render(request, 'core/leave_request_list.html', context)

# Placeholder views for other functionalities
@login_required
def employee_list(request):
    return render(request, 'core/employee_list.html')

@login_required
def employee_create(request):
    return render(request, 'core/employee_form.html')

@login_required
def employee_detail(request, pk):
    return render(request, 'core/employee_detail.html')

@login_required
def employee_edit(request, pk):
    return render(request, 'core/employee_form.html')

@login_required
def employee_delete(request, pk):
    return redirect('core:employee_list')

@login_required
def department_list(request):
    return render(request, 'core/department_list.html')

@login_required
def department_create(request):
    return render(request, 'core/department_form.html')

@login_required
def department_edit(request, pk):
    return render(request, 'core/department_form.html')

@login_required
def department_delete(request, pk):
    return redirect('core:department_list')

@login_required
def designation_list(request):
    return render(request, 'core/designation_list.html')

@login_required
def designation_create(request):
    return render(request, 'core/designation_form.html')

@login_required
def designation_edit(request, pk):
    return render(request, 'core/designation_form.html')

@login_required
def designation_delete(request, pk):
    return redirect('core:designation_list')

@login_required
def leave_type_list(request):
    return render(request, 'core/leave_type_list.html')

@login_required
def leave_type_create(request):
    return render(request, 'core/leave_type_form.html')

@login_required
def leave_type_edit(request, pk):
    return render(request, 'core/leave_type_form.html')

@login_required
def leave_type_delete(request, pk):
    return redirect('core:leave_type_list')

@login_required
def holiday_list(request):
    return render(request, 'core/holiday_list.html')

@login_required
def holiday_create(request):
    return render(request, 'core/holiday_form.html')

@login_required
def holiday_edit(request, pk):
    return render(request, 'core/holiday_form.html')

@login_required
def holiday_delete(request, pk):
    return redirect('core:holiday_list')

@login_required
def leave_request_detail(request, pk):
    return render(request, 'core/leave_request_detail.html')

@login_required
def leave_request_edit(request, pk):
    return render(request, 'core/leave_request_form.html')

@login_required
def leave_request_cancel(request, pk):
    return redirect('core:leave_request_list')

@login_required
def leave_request_approve(request, pk):
    return render(request, 'core/leave_request_approve.html')

@login_required
def leave_balance_list(request):
    return render(request, 'core/leave_balance_list.html')

@login_required
def leave_balance_create(request):
    return render(request, 'core/leave_balance_form.html')

@login_required
def leave_balance_edit(request, pk):
    return render(request, 'core/leave_balance_form.html')

@login_required
def attendance_list(request):
    return render(request, 'core/attendance_list.html')

@login_required
def mark_attendance(request):
    return render(request, 'core/attendance_form.html')

@login_required
def attendance_edit(request, pk):
    return render(request, 'core/attendance_form.html')

@login_required
def attendance_report(request):
    return render(request, 'core/attendance_report.html')

@login_required
def attendance_bulk_upload(request):
    return render(request, 'core/attendance_bulk_upload.html')

@login_required
def attendance_summary(request):
    return JsonResponse({'status': 'success'})

@login_required
def reports_dashboard(request):
    return render(request, 'core/reports_dashboard.html')

@login_required
def leave_summary_report(request):
    return render(request, 'core/leave_summary_report.html')

@login_required
def attendance_summary_report(request):
    return render(request, 'core/attendance_summary_report.html')

@login_required
def employee_wise_report(request):
    return render(request, 'core/employee_wise_report.html')