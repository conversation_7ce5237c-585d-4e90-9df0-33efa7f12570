from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from datetime import date, timedelta
from core.models import (
    Department, Designation, Employee, LeaveType, 
    HolidayCalendar, LeaveBalance
)

User = get_user_model()

class Command(BaseCommand):
    help = 'Setup sample data for Leave & Attendance Management System'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up sample data...'))
        
        # Create Departments
        departments_data = [
            {'name': 'Information Technology', 'description': 'IT Department handling software development and infrastructure'},
            {'name': 'Human Resources', 'description': 'HR Department managing employee relations and policies'},
            {'name': 'Finance & Accounts', 'description': 'Finance department handling accounting and financial operations'},
            {'name': 'Marketing', 'description': 'Marketing department for business development and promotion'},
            {'name': 'Operations', 'description': 'Operations department managing day-to-day business activities'},
        ]
        
        for dept_data in departments_data:
            dept, created = Department.objects.get_or_create(
                name=dept_data['name'],
                defaults={'description': dept_data['description']}
            )
            if created:
                self.stdout.write(f'Created department: {dept.name}')
        
        # Create Designations
        designations_data = [
            {'title': 'Software Engineer', 'description': 'Develops and maintains software applications'},
            {'title': 'Senior Software Engineer', 'description': 'Senior level software development role'},
            {'title': 'Team Lead', 'description': 'Leads a team of developers'},
            {'title': 'Project Manager', 'description': 'Manages projects and coordinates teams'},
            {'title': 'HR Executive', 'description': 'Handles HR operations and employee relations'},
            {'title': 'Accountant', 'description': 'Manages financial records and transactions'},
            {'title': 'Marketing Executive', 'description': 'Handles marketing campaigns and strategies'},
            {'title': 'Operations Manager', 'description': 'Manages operational activities'},
        ]
        
        for desig_data in designations_data:
            desig, created = Designation.objects.get_or_create(
                title=desig_data['title'],
                defaults={'description': desig_data['description']}
            )
            if created:
                self.stdout.write(f'Created designation: {desig.title}')
        
        # Create Leave Types
        leave_types_data = [
            {
                'name': 'Annual Leave',
                'description': 'Yearly vacation leave',
                'max_days_per_year': 21,
                'is_carry_forward': True,
                'max_carry_forward_days': 5
            },
            {
                'name': 'Sick Leave',
                'description': 'Medical leave for illness',
                'max_days_per_year': 12,
                'is_carry_forward': False,
                'max_carry_forward_days': 0
            },
            {
                'name': 'Casual Leave',
                'description': 'Short-term personal leave',
                'max_days_per_year': 12,
                'is_carry_forward': False,
                'max_carry_forward_days': 0
            },
            {
                'name': 'Maternity Leave',
                'description': 'Maternity leave for new mothers',
                'max_days_per_year': 180,
                'is_carry_forward': False,
                'max_carry_forward_days': 0
            },
            {
                'name': 'Paternity Leave',
                'description': 'Paternity leave for new fathers',
                'max_days_per_year': 15,
                'is_carry_forward': False,
                'max_carry_forward_days': 0
            },
            {
                'name': 'Emergency Leave',
                'description': 'Emergency personal leave',
                'max_days_per_year': 5,
                'is_carry_forward': False,
                'max_carry_forward_days': 0
            },
        ]
        
        for leave_data in leave_types_data:
            leave_type, created = LeaveType.objects.get_or_create(
                name=leave_data['name'],
                defaults=leave_data
            )
            if created:
                self.stdout.write(f'Created leave type: {leave_type.name}')
        
        # Create Holidays for 2025
        holidays_data = [
            {'date': date(2025, 1, 1), 'name': 'New Year\'s Day', 'description': 'New Year celebration'},
            {'date': date(2025, 1, 26), 'name': 'Republic Day', 'description': 'Indian Republic Day'},
            {'date': date(2025, 3, 14), 'name': 'Holi', 'description': 'Festival of Colors'},
            {'date': date(2025, 8, 15), 'name': 'Independence Day', 'description': 'Indian Independence Day'},
            {'date': date(2025, 10, 2), 'name': 'Gandhi Jayanti', 'description': 'Mahatma Gandhi\'s Birthday'},
            {'date': date(2025, 10, 31), 'name': 'Diwali', 'description': 'Festival of Lights', 'is_optional': False},
            {'date': date(2025, 12, 25), 'name': 'Christmas Day', 'description': 'Christmas celebration'},
        ]
        
        for holiday_data in holidays_data:
            holiday, created = HolidayCalendar.objects.get_or_create(
                date=holiday_data['date'],
                defaults=holiday_data
            )
            if created:
                self.stdout.write(f'Created holiday: {holiday.name} on {holiday.date}')
        
        # Create sample employee profiles for existing users
        it_dept = Department.objects.get(name='Information Technology')
        hr_dept = Department.objects.get(name='Human Resources')
        
        se_designation = Designation.objects.get(title='Software Engineer')
        hr_designation = Designation.objects.get(title='HR Executive')
        
        # Get existing users and create employee profiles
        users = User.objects.filter(is_deleted=False)
        employee_counter = 1
        
        for user in users:
            try:
                # Check if employee profile already exists
                employee = user.employee_profile
                self.stdout.write(f'Employee profile already exists for: {user.username}')
            except:
                # Create employee profile
                if user.role == 'admin':
                    dept = hr_dept
                    desig = hr_designation
                else:
                    dept = it_dept
                    desig = se_designation
                
                employee = Employee.objects.create(
                    user=user,
                    employee_code=f'EMP{employee_counter:03d}',
                    department=dept,
                    designation=desig,
                    date_of_joining=date.today() - timedelta(days=365),
                    status='Active'
                )
                
                # Create leave balances for current year
                current_year = date.today().year
                leave_types = LeaveType.objects.filter(is_active=True)
                
                for leave_type in leave_types:
                    LeaveBalance.objects.create(
                        employee=employee,
                        leave_type=leave_type,
                        year=current_year,
                        total_allocated=leave_type.max_days_per_year,
                        used_days=0,
                        carry_forward=0
                    )
                
                self.stdout.write(f'Created employee profile: {employee.employee_code} for {user.username}')
                employee_counter += 1
        
        self.stdout.write(self.style.SUCCESS('Sample data setup completed successfully!'))
        self.stdout.write(self.style.WARNING('You can now access the Leave & Attendance Management System.'))
